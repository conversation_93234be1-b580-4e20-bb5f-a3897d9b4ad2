<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat - OPA Viewer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/css/chat.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid h-100">
        <div class="row h-100">
            <div class="col-12 d-flex flex-column h-100">
                <!-- Header do Chat -->
                <div class="chat-header bg-success text-white p-3">
                    <div class="d-flex align-items-center">
                        <a href="/" class="btn btn-outline-light btn-sm me-3">
                            <i class="fas fa-arrow-left"></i> Voltar
                        </a>
                        <div class="flex-grow-1">
                            <h5 class="mb-0" id="chatTitle">
                                <i class="fab fa-whatsapp me-2"></i>Carregando conversa...
                            </h5>
                            <small id="chatInfo">Aguarde...</small>
                        </div>
                        <div>
                            <button class="btn btn-outline-light btn-sm" onclick="exportarPDF()" title="Exportar para PDF">
                                <i class="fas fa-file-pdf me-1"></i>PDF
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Área de Mensagens -->
                <div class="chat-messages flex-grow-1 p-3" id="chatMessages">
                    <div class="text-center">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Carregando mensagens...</span>
                        </div>
                        <p class="mt-2">Carregando conversa...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para visualização de imagens -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close">&times;</span>
        <div class="image-modal-content">
            <img id="modalImage" src="" alt="Imagem">
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const idAtendimento = '<%= id_atendimento %>';
        
        async function carregarChat() {
            try {
                const response = await fetch(`/api/mensagens/${idAtendimento}`);
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }
                
                // Atualizar header com informações mais completas
                const clienteUser = data.atendimento?.cliente_info?.[0];
                const clientePrincipal = data.atendimento?.cliente_principal_info?.[0];

                // Priorizar nome do cliente principal, depois cliente_user, depois das mensagens
                let clienteNome = clientePrincipal?.nome ||
                                clienteUser?.nome ||
                                data.mensagens[0]?.usuario_info?.[0]?.nome ||
                                'Cliente não identificado';

                // Se temos cliente principal, mostrar ID também
                if (clientePrincipal?.id) {
                    clienteNome += ` (ID: ${clientePrincipal.id})`;
                }

                const atendente = data.atendimento?.atendente_info?.[0]?.nome || 'Atendente não identificado';
                const whatsapp = data.atendimento?.canal_cliente || '';
                const dataAtendimento = new Date(data.atendimento?.date).toLocaleDateString('pt-BR');

                document.getElementById('chatTitle').innerHTML =
                    `<i class="fab fa-whatsapp me-2"></i>${clienteNome}`;
                document.getElementById('chatInfo').textContent =
                    `${whatsapp} • Atendente: ${atendente} • ${dataAtendimento} • ${data.mensagens.length} mensagens`;
                
                // Renderizar mensagens
                renderizarMensagens(data.mensagens, data.atendimento);
                
            } catch (error) {
                console.error('Erro ao carregar chat:', error);
                document.getElementById('chatMessages').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erro ao carregar a conversa: ${error.message}
                    </div>
                `;
            }
        }
        
        function renderizarMensagens(mensagens, atendimento) {
            const container = document.getElementById('chatMessages');
            
            if (mensagens.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-comments fa-3x mb-3"></i>
                        <h5>Nenhuma mensagem encontrada</h5>
                        <p>Esta conversa não possui mensagens registradas.</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            let dataAnterior = '';
            
            mensagens.forEach(msg => {
                const data = new Date(msg.data);
                const dataFormatada = data.toLocaleDateString('pt-BR');
                const horaFormatada = data.toLocaleTimeString('pt-BR', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                });
                
                // Separador de data
                if (dataFormatada !== dataAnterior) {
                    html += `
                        <div class="date-separator">
                            <span class="date-badge">${dataFormatada}</span>
                        </div>
                    `;
                    dataAnterior = dataFormatada;
                }
                
                // Determinar se é mensagem do cliente, atendente ou assistente virtual
                const isCliente = msg.usuario_info && msg.usuario_info.length > 0;
                const isAtendente = msg.atendente_info && msg.atendente_info.length > 0;

                let nomeRemetente = 'Sistema';
                if (isCliente) {
                    nomeRemetente = msg.usuario_info[0].nome;
                } else if (isAtendente) {
                    nomeRemetente = msg.atendente_info[0].nome;
                } else if (!msg.id_user) {
                    // Mensagem sem id_user pode ser da assistente virtual
                    nomeRemetente = 'Atendente';
                }
                
                // Conteúdo da mensagem
                let conteudoMensagem = '';

                // Verificar se há mensagem de texto
                if (msg.mensagem && typeof msg.mensagem === 'string' && msg.mensagem.trim()) {
                    conteudoMensagem += `<div class="message-text">${msg.mensagem.replace(/\n/g, '<br>')}</div>`;
                }

                // Verificar se há arquivo anexado
                if (msg.arquivo_info && msg.arquivo_info.length > 0) {
                    const arquivo = msg.arquivo_info[0];
                    conteudoMensagem += renderizarArquivo(arquivo);
                }

                // Verificar se há citação de mensagem
                if (msg.mensagem_citada_info && msg.mensagem_citada_info.length > 0) {
                    const msgCitada = msg.mensagem_citada_info[0];
                    conteudoMensagem = `
                        <div class="message-quote">
                            <div class="quote-content">${msgCitada.mensagem || '[Mensagem citada]'}</div>
                        </div>
                        ${conteudoMensagem}
                    `;
                }

                // Se ainda não há conteúdo, verificar outros campos possíveis
                if (!conteudoMensagem) {
                    // Verificar se é mensagem de sistema/menu que pode estar em outros campos
                    if (msg.tipo === 'menu' || msg.tipo === 'opcoes' || !msg.id_user) {
                        conteudoMensagem = '<div class="message-text system-message">[Mensagem de menu/sistema - conteúdo não disponível]</div>';
                    } else {
                        conteudoMensagem = '<div class="message-text">[Mensagem sem conteúdo]</div>';
                    }
                }

                html += `
                    <div class="message ${isCliente ? 'client' : 'agent'}">
                        <div class="message-content">
                            <div class="message-header">
                                <strong>${nomeRemetente}</strong>
                                <span class="message-time">${horaFormatada}</span>
                            </div>
                            ${conteudoMensagem}
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
            // Scroll para o final
            container.scrollTop = container.scrollHeight;
        }
        
        function renderizarArquivo(arquivo) {
            const isImage = arquivo.tipo && arquivo.tipo.startsWith('image/');
            const isVideo = arquivo.tipo && arquivo.tipo.startsWith('video/');
            const isAudio = arquivo.tipo && arquivo.tipo.startsWith('audio/');

            if (isImage) {
                return `
                    <div class="message-media">
                        <img src="/arquivo/${arquivo._id}"
                             alt="${arquivo.nome}"
                             class="message-image"
                             onclick="abrirModalImagem('/arquivo/${arquivo._id}', '${arquivo.nome}')">
                    </div>
                `;
            } else if (isVideo) {
                return `
                    <div class="message-media">
                        <video controls style="max-width: 100%; height: auto;">
                            <source src="/arquivo/${arquivo._id}" type="${arquivo.tipo}">
                            Seu navegador não suporta vídeo.
                        </video>
                    </div>
                `;
            } else if (isAudio) {
                return `
                    <div class="message-media">
                        <div class="audio-player" data-audio-id="${arquivo._id}">
                            <div class="audio-controls">
                                <button class="audio-play-btn" onclick="toggleAudio('${arquivo._id}')">
                                    <i class="fas fa-play"></i>
                                </button>
                            </div>
                            <div class="audio-progress" onclick="seekAudio(event, '${arquivo._id}')">
                                <div class="audio-progress-bar"></div>
                            </div>
                            <div class="audio-time">0:00</div>
                            <audio preload="metadata" style="display: none;">
                                <source src="/arquivo/${arquivo._id}" type="${arquivo.tipo}">
                            </audio>
                        </div>
                    </div>
                `;
            } else {
                // Arquivo genérico
                const fileIcon = getFileIcon(arquivo.tipo);
                const fileSize = formatFileSize(arquivo.size);

                return `
                    <div class="message-media">
                        <a href="/arquivo/${arquivo._id}" class="message-file" target="_blank">
                            <i class="${fileIcon} file-icon"></i>
                            <div class="file-info">
                                <div class="file-name">${arquivo.nome}</div>
                                <div class="file-size">${fileSize}</div>
                            </div>
                            <button type="button" class="download-btn" title="Download">
                                <i class="fas fa-download"></i>
                            </button>
                        </a>
                    </div>
                `;
            }
        }

        function getFileIcon(tipo) {
            if (!tipo) return 'fas fa-file';

            if (tipo.includes('pdf')) return 'fas fa-file-pdf';
            if (tipo.includes('word') || tipo.includes('document')) return 'fas fa-file-word';
            if (tipo.includes('excel') || tipo.includes('spreadsheet')) return 'fas fa-file-excel';
            if (tipo.includes('powerpoint') || tipo.includes('presentation')) return 'fas fa-file-powerpoint';
            if (tipo.includes('audio')) return 'fas fa-file-audio';
            if (tipo.includes('video')) return 'fas fa-file-video';
            if (tipo.includes('image')) return 'fas fa-file-image';
            if (tipo.includes('text')) return 'fas fa-file-alt';
            if (tipo.includes('zip') || tipo.includes('rar')) return 'fas fa-file-archive';

            return 'fas fa-file';
        }

        function formatFileSize(bytes) {
            if (!bytes) return '0 B';

            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function abrirModalImagem(src, alt) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');

            modal.style.display = 'block';
            modalImg.src = src;
            modalImg.alt = alt;
        }

        // Fechar modal de imagem
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('imageModal');
            const closeBtn = document.querySelector('.image-modal-close');

            closeBtn.onclick = function() {
                modal.style.display = 'none';
            }

            modal.onclick = function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            }
        });

        function exportarPDF() {
            const url = `/print/chat/${idAtendimento}`;
            window.open(url, '_blank');
        }

        // Funções para controle de áudio
        let currentAudio = null;
        let currentAudioId = null;

        function toggleAudio(audioId) {
            const audioPlayer = document.querySelector(`[data-audio-id="${audioId}"]`);
            const audio = audioPlayer.querySelector('audio');
            const playBtn = audioPlayer.querySelector('.audio-play-btn i');
            const progressBar = audioPlayer.querySelector('.audio-progress-bar');
            const timeDisplay = audioPlayer.querySelector('.audio-time');

            // Pausar áudio atual se houver outro tocando
            if (currentAudio && currentAudioId !== audioId) {
                currentAudio.pause();
                const currentPlayer = document.querySelector(`[data-audio-id="${currentAudioId}"]`);
                if (currentPlayer) {
                    currentPlayer.querySelector('.audio-play-btn i').className = 'fas fa-play';
                }
            }

            if (audio.paused) {
                audio.play();
                playBtn.className = 'fas fa-pause';
                currentAudio = audio;
                currentAudioId = audioId;

                // Atualizar progresso
                audio.addEventListener('timeupdate', () => updateProgress(audioId));
                audio.addEventListener('ended', () => {
                    playBtn.className = 'fas fa-play';
                    progressBar.style.width = '0%';
                    timeDisplay.textContent = formatTime(audio.duration);
                    currentAudio = null;
                    currentAudioId = null;
                });

                // Mostrar duração quando carregado
                audio.addEventListener('loadedmetadata', () => {
                    timeDisplay.textContent = formatTime(audio.duration);
                });
            } else {
                audio.pause();
                playBtn.className = 'fas fa-play';
                currentAudio = null;
                currentAudioId = null;
            }
        }

        function updateProgress(audioId) {
            const audioPlayer = document.querySelector(`[data-audio-id="${audioId}"]`);
            const audio = audioPlayer.querySelector('audio');
            const progressBar = audioPlayer.querySelector('.audio-progress-bar');
            const timeDisplay = audioPlayer.querySelector('.audio-time');

            if (audio.duration) {
                const progress = (audio.currentTime / audio.duration) * 100;
                progressBar.style.width = progress + '%';
                timeDisplay.textContent = formatTime(audio.currentTime);
            }
        }

        function seekAudio(event, audioId) {
            const audioPlayer = document.querySelector(`[data-audio-id="${audioId}"]`);
            const audio = audioPlayer.querySelector('audio');
            const progressContainer = event.currentTarget;
            const rect = progressContainer.getBoundingClientRect();
            const clickX = event.clientX - rect.left;
            const width = rect.width;
            const percentage = clickX / width;

            if (audio.duration) {
                audio.currentTime = percentage * audio.duration;
            }
        }

        function formatTime(seconds) {
            if (isNaN(seconds)) return '0:00';
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        // Carregar chat quando a página carregar
        document.addEventListener('DOMContentLoaded', carregarChat);
    </script>
</body>
</html>
